import { getAllUnBindPhoneUsers } from '../addPhone'
import { manageESQuery<PERSON><PERSON>son, deleteQueryFromESbyJson, downloadDataFromES } from '../manageESQuerybyJson'
import * as path from 'path'
import { FileHelper } from '../../../bot/lib/file'
import { RAGHelper } from '../../../bot/model/rag/rag'


describe('Test', function () {
  beforeAll(() => {
  })


  it('createIndex', async () => {
    await RAGHelper.createRAG('haogu_general_rag', ['q', 'a', 'doc'], 'q')
  }, 9e8)

  it('使用JSON批量添加query到ES', async () => {
    await manageESQuerybyJson(
      'haogu_general_rag',
      path.join('/Users/<USER>/Desktop/code/wechaty_bot/test/tools/haogu/rag/filter_rag.json'))
  }, 9E8)

  it('使用JSON批量删除', async () => {
    await deleteQueryFromESbyJson(
      'moer_rag_1',
      path.join('/Users/<USER>/Downloads/JYS_Workspace/Moer/wechaty_bot/dev/moer_search/RagJson文件汇总/deleteQuery.json'))
  }, 9E8)

  it('批量下载知识库', async () => {
    await downloadDataFromES(
      'moer_rag_2_2048d',
      path.join('/Users/<USER>/Desktop/code/wechaty_bot/test/tools/ragDoc'))
  }, 9E8)


  it('removeDuplicate', async () => {
    const normals = JSON.parse(await FileHelper.readFile('/Users/<USER>/Desktop/code/wechaty_bot/test/tools/ragDoc/常规问题全局.json'))
    const systems = JSON.parse(await FileHelper.readFile('/Users/<USER>/Desktop/code/wechaty_bot/test/tools/ragDoc/常规问题周三八点前.json'))

    // 创建常规问题的Set用于快速查找
    const commonQuestionsSet = new Set(normals.map((item) => item.q))

    // 过滤出同时存在于两个文件中的问题
    const filteredData = systems.filter((item) => commonQuestionsSet.has(item.q))

    const res: any[] = []

    for (const item of normals) {
      if (!filteredData.find((filteredItem) => filteredItem.q === item.q)) {
        res.push(item)
      }
    }

    console.log(JSON.stringify(res))
    await FileHelper.writeFile('/Users/<USER>/Desktop/code/wechaty_bot/test/tools/ragDoc/常规问题全局.json', JSON.stringify(res))

  }, 9e8)
})