'use server'
import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'

export async function queryAccounts() {
  'use server'
  const mongoClient = PrismaMongoClient.getConfigInstance()
  const configs = await mongoClient.config.findMany({ select:{ id:true, accountName:true, wechatId:true }, where:{ enterpriseName:'moer' } })
  return configs
}

export async function getAccountByWechatId(wechatId: string) {
  'use server'
  const mongoClient = PrismaMongoClient.getConfigInstance()
  const config = await mongoClient.config.findFirst({
    select: { accountName: true, wechatId: true },
    where: {
      enterpriseName: 'moer',
      wechatId: wechatId
    }
  })
  return config
}

export async function getCurrentCourseNo() {
  'use server'
  const mongoClient = PrismaMongoClient.getInstance()
  const courseInfo = await mongoClient.moer_lesson.findUnique({
    where: {
      id: '66cf123043ea223ac75d83da'
    },
    select: {
      lesson_no: true
    }
  })
  return courseInfo?.lesson_no || null
}