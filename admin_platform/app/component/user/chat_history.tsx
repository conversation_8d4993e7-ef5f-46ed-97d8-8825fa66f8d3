import dayjs from 'dayjs'
import Link from 'next/link'
import { useState, useEffect, Dispatch, SetStateAction, ReactNode, ReactElement } from 'react'
import { FaPen } from 'react-icons/fa6'
import { RiRocket2Fill } from 'react-icons/ri'
import { toast } from 'react-toastify'
import { AnimatePresence, motion } from 'motion/react'
import { DashboardData, DashboardDataWithChatHistory } from '@/app/type/dashboard_data'
import { DashboardTag } from '@/app/type/dashboard_tag'
import { TbMoodLookDown } from 'react-icons/tb'
import { IoMdArrowDropdown } from 'react-icons/io'
import { FaTimes } from 'react-icons/fa'
import { UserData } from '@/app/type/user'
import { ChatHistory as ChatHistoryData } from '@/app/type/chat_history'
import { LogStore } from '@/app/type/log_store'

export function ChatHistory(
  {
    id,
    langsmithProjectId,
    queryChatHistoryByChatId,
    queryLogByChatId,
    queryChatById,
    createManyDashboardData,
    queryDashboardDataByChatId,
    queryAllDashboardTags,
    findOrCreateDashboardTag
  }:
  {
    id:string,
    queryChatHistoryByChatId(chatId: string): Promise<ChatHistoryData[]>
    queryLogByChatId(chatId: string): Promise<LogStore[]>
    queryChatById(chatId: string): Promise<UserData | null>
    langsmithProjectId: string
    createManyDashboardData(dashboardData:Omit<DashboardData, 'id' | 'created_at'>[]):Promise<void>
    queryDashboardDataByChatId(chatId:string): Promise<DashboardDataWithChatHistory[]>
    queryAllDashboardTags(): Promise<DashboardTag[]>
    findOrCreateDashboardTag(name: string): Promise<DashboardTag>
  }) {
  const [openForm, setOpenForm] = useState<boolean>(false)
  const [selectedChatHistoryId, setSelectedChatHistoryId] = useState<string[]>([])
  const [chatHistory, setChatHistory] = useState<ChatHistoryData[]>([])
  const [chatLog, setChatLog] = useState<LogStore[]>([])
  const [chatData, setChatData] = useState<UserData | null>(null)
  const [firstLoad, setFirstLoad] = useState<boolean>(false)
  const [selectedDashboardDataHistoryId, setSelectedDashboardDataHistoryId] = useState<string[]>([])

  useEffect(() => {
    toast.promise(Promise.all([
      queryChatHistoryByChatId(id),
      queryLogByChatId(id),
      queryChatById(id)
    ]), {
      pending:'query pending',
      success: 'query success',
      error: {
        render:(e) => {
          return `${e.data}`
        }
      }
    }).then(([history, logs, chat]) => {
      setChatHistory(history)
      setChatLog(logs)
      setChatData(chat)
    }).finally(() => {
      setFirstLoad(true)
    })
  }, [id])
  useEffect(() => {
    if (window.location.hash) {
      const anchor = document.getElementById(window.location.hash.substring(1))
      if (anchor) {
        anchor.scrollIntoView({ block:'center' })
        anchor.focus()
      }
    } else {
      window.scrollTo(0, document.body.scrollHeight)
    }
  }, [chatHistory, chatLog])

  if (!firstLoad) {
    return <span className="loading loading-dots loading-xl"></span>
  }
  let i = 0
  let j = 0
  const arr:ReactElement[] = []
  while (i < chatHistory.length || j < chatLog.length) {
    let type: 'chatHistory' | 'chatLog' = 'chatHistory'
    if (j >= chatLog.length) {
      type = 'chatHistory'
    } else if (i >= chatHistory.length) {
      type = 'chatLog'
    } else if (dayjs(chatHistory[i].created_at).isBefore(dayjs(chatLog[j].timestamp))) {
      type = 'chatHistory'
    } else {
      type = 'chatLog'
    }
    if (type == 'chatHistory') {
      const detail = chatHistory[i]
      arr.push(
        <li
          key={detail.id}
        >
          <hr />
          <div className={`timeline-start timeline-box w-full border-0 shadow-none ${selectedDashboardDataHistoryId.includes(detail.id) ? (detail.role == 'user' ? 'border-l-2 rounded-l-none border-info' : ' border-r-2 rounded-r-none border-info') : '' }`}>
            <div
              className={`chat ${detail.role == 'user' ? 'chat-start' : 'chat-end'}`}
            >
              <div className="chat-header items-center">
                {detail.role == 'assistant' && <a className='text-neutral-content hover:text-neutral duration-200 focus:text-secondary' href={`./${detail.chat_id}#${detail.id}`} id={detail.id}>#</a>}
                {detail.is_recalled && <div className='h-4 w-4 bg-error text-center rounded-full text-base-300 tooltip' data-tip="recalled">!</div>}
                {detail.is_send_by_human && <div className='h-4 w-4 bg-info text-center rounded-full text-base-300 tooltip' data-tip="sent by human">人</div>}
                <div>{dayjs(detail.created_at).format('YYYY-MM-DD HH:mm:ss')}</div>
                {detail.role == 'user' && <a className='text-neutral-content hover:text-neutral duration-200 focus:text-secondary' href={`./${detail.chat_id}#${detail.id}`} id={detail.id}>#</a>}
                {detail.round_id && <Link target='_blank' href={`https://smith.langchain.com/o/4a322f6a-86c2-4f11-b84f-50af060d25ec/projects/p/${langsmithProjectId}?columnVisibilityModel=%7B%22feedback_stats%22%3Atrue%2C%22reference_example%22%3Afalse%7D&timeModel=%7B%22duration%22%3A%227d%22%7D&searchModel=%7B%22filter%22%3A%22and%28eq%28run_type%2C+%5C%22llm%5C%22%29%2C+and%28eq%28metadata_key%2C+%5C%22round_id%5C%22%29%2C+eq%28metadata_value%2C+%5C%22${detail.round_id}%5C%22%29%29%29%22%7D`} className='btn btn-xs btn-success btn-soft'>lang</Link>}
                {detail.chat_state?.state && <div className="tooltip tooltip-left">
                  <div className='tooltip-content'>
                    {Object.entries(detail.chat_state.state).map(([k, v], index) => {
                      return <div key={index}>{k}:{v == true ? 'true' : 'false'}</div>
                    })}
                  </div>
                  <button className="btn btn-xs btn-info btn-soft">state</button>
                </div>}
                {detail.chat_state?.userSlots && <div className="tooltip tooltip-left">
                  <div className='tooltip-content'>
                    {Object.entries(detail.chat_state.userSlots).map(([k, v], index) => {
                      return <div key={index}>{k}:{JSON.stringify(v)}</div>
                    })}
                  </div>
                  <button className="btn btn-xs btn-info btn-soft">slots</button>
                </div>}
              </div>
              <div className="chat-bubble max-w-[calc(23dvw)] whitespace-pre-line text-base">
                {detail.content}
              </div>
              <AnimatePresence>
                {openForm &&
              <motion.div key={detail.id} exit={{ scale:0, opacity:0, width:0, height:0 }} initial={{ scale:0, opacity:0, width:0, height:0 }} animate={{ scale:1, opacity:1, width:'auto', height:'auto' }} className='row-2 self-center'><input type='checkbox' className='checkbox' checked={selectedChatHistoryId.includes(detail.id)} onChange={(e) => {
                const checked = e.currentTarget.checked
                setSelectedChatHistoryId((pre) => {
                  if (checked) {
                    pre.push(detail.id)
                    return [...new Set(pre)]
                  } else {
                    return [...pre.filter((item) => item != detail.id)]
                  }
                }) }}/></motion.div>
                }
              </AnimatePresence>
            </div>
          </div>
          <hr />
        </li>
      )
      i++
    } else {
      const detail = chatLog[j]
      arr.push(
        <div
          key={`log${detail.id}`}
          className={'chat chat-start'}
        >
          <div className="chat-header">{dayjs(detail.timestamp).format('YYYY-MM-DD HH:mm:ss')}</div>
          <div className="chat-bubble whitespace-pre-line">
            {detail.msg}
          </div>
        </div>
      )
      j++
    }
  }
  const minimized:ReactElement[] = []
  let stack:ReactElement[] = []
  for (const talk of arr) {
    if (talk?.key?.includes('log')) {
      stack.push(talk)
    } else {
      if (stack.length > 0) {
        const group = <Minimize arr={[...stack]} key={stack[0]!.key}/>
        minimized.push(group)
        stack = []
      }
      minimized.push(talk)
    }
  }

  return (
    <div className='flex flex-col p-2 gap-2'>
      {/* Customer Header */}
      {chatData && (
        <div className='bg-base-200 rounded-lg p-4 mb-4'>
          <div className='flex items-center gap-4'>
            <div className='flex flex-col'>
              <h1 className='text-2xl font-bold text-primary'>
                {chatData.contact.wx_name}
              </h1>
              <div className='flex gap-2 mt-2'>
                <div className="badge badge-soft badge-primary">ID: {chatData.id}</div>
                {chatData.phone && <div className="badge badge-soft badge-info">手机号: {chatData.phone}</div>}
                {chatData.course_no && <div className="badge badge-soft badge-accent">期数: {chatData.course_no}</div>}
                <div className="badge badge-soft badge-success">微信ID: {chatData.wx_id}</div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className='flex gap-2'>
        <ul className="timeline timeline-vertical">
          {...minimized}
        </ul>
        <DashboardDataAddButonAndForm
          setSelectedChatHistoryId={setSelectedChatHistoryId}
          chatId={id}
          createManyDashboardData={createManyDashboardData}
          queryAllDashboardTags={queryAllDashboardTags}
          findOrCreateDashboardTag={findOrCreateDashboardTag}
          openForm={openForm}
          setOpenForm={setOpenForm}
          selectedChatHistoryId={selectedChatHistoryId}
        />
        <DashboardDataButonAndShow setSelectedDashboardDataHistoryId={setSelectedDashboardDataHistoryId} chatId={id} queryDashboardDataByChatId={queryDashboardDataByChatId}/>
        <Rocket/>
      </div>
    </div>)
}

function Minimize({ arr }:{arr:ReactNode[]}) {
  const [isOpen, setIsOpen] = useState<boolean>(false)
  return <li>
    <hr />
    <div className='timeline-end timeline-box border-0 shadow-none'>
      <button className='btn btn-soft btn-accent h-4 ' onClick={() => {
        setIsOpen((open) => !open)
      }}>
        <motion.div animate={{ rotate:isOpen ? 180 : 0 }}>
          <IoMdArrowDropdown/>
        </motion.div>
      </button>
      <motion.div animate={{ height:isOpen ? 'auto' : 0 }} initial={{ height:0 }} className='overflow-hidden'>
        {...arr}
      </motion.div>

    </div>
    <hr />
  </li>
}

function Rocket() {
  return <button
    className="btn btn-neutral btn-circle fixed right-10 bottom-10"
    onClick={() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }}
  >
    <RiRocket2Fill size={24}/>
  </button>
}

function DashboardDataButonAndShow({
  chatId,
  queryDashboardDataByChatId,
  setSelectedDashboardDataHistoryId
}:{
  chatId:string
  queryDashboardDataByChatId(chatId:string): Promise<DashboardDataWithChatHistory[]>
  setSelectedDashboardDataHistoryId: Dispatch<SetStateAction<string[]>>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const [openForm, setOpenForm] = useState<boolean>(false)
  const [dashboard, setDashboard] = useState<DashboardDataWithChatHistory[]>([])
  const [isInit, setIsInit] = useState<boolean>(false)
  return <div>
    <div className='fixed right-30 bottom-30'>
      <motion.div
        initial={false}
        animate={{
          opacity: openForm ? [null, 1] : [null, 0],
          scaleX: openForm ? [null, 1] : [null, 0],
          scaleY: openForm ? [null, 1] : [null, 0],
          transition: {
            duration: 0.2,
          },
          transformOrigin: 'bottom right',
        }}
        className="top-0 left-0 rounded-md border border-gray-200 bg-base-200 shadow p-2 min-w-[20rem] max-w-[35rem] min-h-[20rem]"
      >
        {loading ? <span className="loading loading-dots loading-xl"></span>
          : <div>
            {dashboard.length == 0 ? <div className="text-gray-500 text-center py-4">暂无标注</div> : <div className='flex flex-col gap-3'>
              <h3 className="text-lg font-semibold text-gray-700">标注记录</h3>
              {dashboard.map((item) => {
                return <div key={item.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex-1">
                      {/* 显示标签 */}
                      {item.tags && item.tags.length > 0 && (
                        <div className='flex gap-2 flex-wrap mb-2'>
                          {item.tags.map((tag) => (
                            <div
                              key={tag.id}
                              className="px-2 py-1 rounded-full text-xs border"
                              style={{ backgroundColor: tag.color || '#E3F2FD' }}
                            >
                              {tag.name}
                            </div>
                          ))}
                        </div>
                      )}
                      {/* 显示描述 */}
                      <p className='text-sm text-gray-800 whitespace-pre-wrap leading-relaxed'>{item.description}</p>
                      <p className='text-xs text-gray-400 mt-2'>{dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss')}</p>
                    </div>
                    <div className='ml-4'>
                      <Link href={`#${item.chat_history[0]?.id || ''}`}>
                        <button
                          className='btn btn-xs btn-info'
                          onClick={() => {
                            setSelectedDashboardDataHistoryId([...item.chat_history.map((historyItem) => historyItem.id)])
                          }}
                          title="跳转到相关聊天记录"
                        >
                          定位
                        </button>
                      </Link>
                    </div>
                  </div>
                </div>
              })}
            </div>}
          </div>}

      </motion.div>
    </div>
    <button className='fixed right-20 bottom-20 btn btn-neutral btn-circle' onClick={() => {
      setOpenForm((state) => !state)
      if (!loading && !isInit) {
        setLoading(true)
        toast.promise(queryDashboardDataByChatId(chatId), {
          pending:'query pending',
          success:'query success',
          error:'query error'
        }).then((res) => {
          setDashboard(res)
        }).finally(() => {
          setLoading(false)
          setIsInit(true)
        })
      }
    }}><TbMoodLookDown /></button>
  </div>
}

function DashboardDataAddButonAndForm({
  setSelectedChatHistoryId,
  chatId,
  createManyDashboardData,
  queryAllDashboardTags,
  findOrCreateDashboardTag,
  openForm,
  setOpenForm,
  selectedChatHistoryId
}:{
  setSelectedChatHistoryId: Dispatch<SetStateAction<string[]>>
  chatId: string
  createManyDashboardData(dashboardData:Omit<DashboardData, 'id' | 'created_at'>[]):Promise<void>
  queryAllDashboardTags(): Promise<DashboardTag[]>
  findOrCreateDashboardTag(name: string): Promise<DashboardTag>
  openForm: boolean
  setOpenForm: Dispatch<SetStateAction<boolean>>
  selectedChatHistoryId: string[]
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const [tags, setTags] = useState<DashboardTag[]>([])
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [description, setDescription] = useState<string>('')
  const [newTagName, setNewTagName] = useState<string>('')

  useEffect(() => {
    if (openForm) {
      queryAllDashboardTags().then(setTags)
    }
  }, [openForm])

  const handleSubmit = async () => {
    if (!description.trim() || selectedChatHistoryId.length === 0) {
      toast.error('请填写描述并选择聊天记录')
      return
    }

    setLoading(true)
    try {
      const dashboardData = {
        chat_id: chatId,
        description: description.trim(),
        chat_history_ids: selectedChatHistoryId,
        tag_ids: selectedTags
      }

      await createManyDashboardData([dashboardData])
      toast.success('标注添加成功')
      setDescription('')
      setSelectedTags([])
      setSelectedChatHistoryId([])
      setOpenForm(false)
    } catch (error) {
      toast.error('标注添加失败')
    } finally {
      setLoading(false)
    }
  }

  const handleAddNewTag = async () => {
    if (!newTagName.trim()) return

    try {
      const newTag = await findOrCreateDashboardTag(newTagName.trim())
      setTags(prev => [...prev, newTag])
      setSelectedTags(prev => [...prev, newTag.id])
      setNewTagName('')
      toast.success('标签添加成功')
    } catch (error) {
      toast.error('标签添加失败')
    }
  }

  return (
    <div className='fixed right-40 bottom-40'>
      <AnimatePresence>
        {openForm && (
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            className="bg-base-200 rounded-lg p-4 shadow-lg min-w-[300px] mb-4"
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">添加标注</h3>
              <button
                className="btn btn-sm btn-circle"
                onClick={() => setOpenForm(false)}
              >
                <FaTimes />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="label">
                  <span className="label-text">描述</span>
                </label>
                <textarea
                  className="textarea textarea-bordered w-full"
                  placeholder="请输入标注描述..."
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>

              <div>
                <label className="label">
                  <span className="label-text">标签</span>
                </label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {tags.map((tag) => (
                    <label key={tag.id} className="cursor-pointer">
                      <input
                        type="checkbox"
                        className="checkbox checkbox-sm mr-1"
                        checked={selectedTags.includes(tag.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedTags(prev => [...prev, tag.id])
                          } else {
                            setSelectedTags(prev => prev.filter(id => id !== tag.id))
                          }
                        }}
                      />
                      <span className="text-sm">{tag.name}</span>
                    </label>
                  ))}
                </div>

                <div className="flex gap-2">
                  <input
                    type="text"
                    className="input input-sm flex-1"
                    placeholder="新标签名称"
                    value={newTagName}
                    onChange={(e) => setNewTagName(e.target.value)}
                  />
                  <button
                    className="btn btn-sm btn-primary"
                    onClick={handleAddNewTag}
                  >
                    添加
                  </button>
                </div>
              </div>

              <div className="text-sm text-gray-500">
                已选择 {selectedChatHistoryId.length} 条聊天记录
              </div>

              <div className="flex gap-2">
                <button
                  className="btn btn-primary flex-1"
                  onClick={handleSubmit}
                  disabled={loading}
                >
                  {loading ? '提交中...' : '提交'}
                </button>
                <button
                  className="btn btn-ghost"
                  onClick={() => setOpenForm(false)}
                >
                  取消
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <button
        className="btn btn-primary btn-circle"
        onClick={() => setOpenForm(!openForm)}
      >
        <FaPen />
      </button>
    </div>
  )
}